import React, { useState } from "react";
import * as Icons from "lucide-react";
import DraggableDataItem from "./DraggableDataItem";

interface DataTreeProps {
  nodeId: string;
  nodeName: string;
  data: unknown;
  maxDepth?: number;
  className?: string;
}

interface TreeNodeProps {
  nodeId: string;
  nodeName: string;
  path: string;
  displayPath: string;
  value: unknown;
  depth: number;
  maxDepth: number;
  isLast?: boolean;
}

const TreeNode: React.FC<TreeNodeProps> = ({
  nodeId,
  nodeName,
  path,
  displayPath,
  value,
  depth,
  maxDepth,
  isLast = false,
}) => {
  const [isExpanded, setIsExpanded] = useState(depth < 2); // Auto-expand first 2 levels

  const isExpandable = (val: unknown): boolean => {
    return (
      val !== null &&
      val !== undefined &&
      typeof val === "object" &&
      (Array.isArray(val) ? val.length > 0 : Object.keys(val as object).length > 0)
    );
  };

  const getChildEntries = (
    val: unknown,
  ): Array<{ key: string; value: unknown; path: string; displayPath: string }> => {
    if (!isExpandable(val)) return [];

    if (Array.isArray(val)) {
      return val.map((item, index) => ({
        key: `[${index}]`,
        value: item,
        path: `${path}[${index}]`,
        displayPath: `${displayPath}[${index}]`,
      }));
    }

    return Object.entries(val as Record<string, unknown>).map(([key, childValue]) => ({
      key,
      value: childValue,
      path: path === "$" ? `$.${key}` : `${path}.${key}`,
      displayPath: displayPath === "$" ? key : `${displayPath}.${key}`,
    }));
  };

  const getValueTypeIcon = (val: unknown) => {
    if (val === null || val === undefined) return Icons.Minus;
    if (typeof val === "string") return Icons.Type;
    if (typeof val === "number") return Icons.Hash;
    if (typeof val === "boolean") return Icons.ToggleLeft;
    if (Array.isArray(val)) return Icons.List;
    if (typeof val === "object") return Icons.Braces;
    return Icons.HelpCircle;
  };

  const expandable = isExpandable(value);
  const childEntries = expandable ? getChildEntries(value) : [];
  const ValueIcon = getValueTypeIcon(value);

  // Don't render if we've exceeded max depth
  if (depth > maxDepth) return null;

  return (
    <div className="select-none">
      {/* Current node */}
      <div className="flex items-center space-x-1">
        {/* Indentation */}
        {depth > 0 && (
          <div className="flex items-center">
            {Array.from({ length: depth }).map((_, i) => (
              <div key={i} className="w-4 flex justify-center">
                {i === depth - 1 ? (
                  <div className="w-px h-4 bg-gray-300" />
                ) : (
                  <div className="w-px h-4 bg-gray-200" />
                )}
              </div>
            ))}
          </div>
        )}

        {/* Expand/collapse button */}
        {expandable ? (
          <button
            onClick={() => setIsExpanded(!isExpanded)}
            className="w-4 h-4 flex items-center justify-center hover:bg-gray-100 rounded"
          >
            {isExpanded ? (
              <Icons.ChevronDown size={12} className="text-gray-600" />
            ) : (
              <Icons.ChevronRight size={12} className="text-gray-600" />
            )}
          </button>
        ) : (
          <div className="w-4" />
        )}

        {/* Node content */}
        <div className="flex-1 min-w-0">
          {expandable && depth < maxDepth ? (
            // Expandable container - show summary
            <div className="flex items-center space-x-2 py-1">
              <ValueIcon size={14} className="text-gray-500 flex-shrink-0" />
              <span className="text-sm font-medium text-gray-700">{displayPath || "root"}</span>
              <span className="text-xs text-gray-500 bg-gray-100 px-2 py-1 rounded">
                {Array.isArray(value) ? `Array(${value.length})` : "Object"}
              </span>
            </div>
          ) : (
            // Leaf node - draggable
            <DraggableDataItem
              nodeId={nodeId}
              nodeName={nodeName}
              dataPath={path}
              value={value}
              displayName={displayPath || "root"}
              className="my-1"
            />
          )}
        </div>
      </div>

      {/* Children */}
      {expandable && isExpanded && depth < maxDepth && (
        <div className="ml-2">
          {childEntries.map((entry, index) => (
            <TreeNode
              key={entry.key}
              nodeId={nodeId}
              nodeName={nodeName}
              path={entry.path}
              displayPath={entry.displayPath}
              value={entry.value}
              depth={depth + 1}
              maxDepth={maxDepth}
              isLast={index === childEntries.length - 1}
            />
          ))}
        </div>
      )}
    </div>
  );
};

const DataTree: React.FC<DataTreeProps> = ({
  nodeId,
  nodeName,
  data,
  maxDepth = 3,
  className = "",
}) => {
  if (data === null || data === undefined) {
    return <div className={`text-sm text-gray-500 p-4 ${className}`}>No data available</div>;
  }

  return (
    <div className={`space-y-1 ${className}`}>
      <TreeNode
        nodeId={nodeId}
        nodeName={nodeName}
        path="$"
        displayPath=""
        value={data}
        depth={0}
        maxDepth={maxDepth}
      />
    </div>
  );
};

export default DataTree;
